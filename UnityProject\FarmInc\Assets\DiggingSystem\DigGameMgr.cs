using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Qarth;
using Lean.Pool;
public class DigGameMgr : TMonoSingleton<DigGameMgr>
{
	private enum PointState
	{
		die,                        //已经废弃
		wait,                       //等待检测
		collid                      //正在使用
	}

	private class PointData
	{
		public Vector2 pos;               //点位置
		public PointState status;          //点状态
		public GameObject col;          //点实例
	}

	public Vector2 scale = new Vector2(1, 1);
	public Vector2 area = new Vector2(1, 1);
	private List<PointData> listPoint = new List<PointData>();
	public GameObject target;
	public List<SpriteEraser> spriteErasers;
	public float ColliderSize = 0.1f;
	public float Circle1Range1 = 1f;
	public float Circle1Range2 = 2f;
	public Camera mainCam;
	public DiggerHead diggerHead;
	public GameObject goPreCollider;

	private void Start()
	{
		CreatePoint();
	}


	private void CreatePoint()
	{
		int line = (int)(scale.x * 100);
		int row = (int)(scale.y * 100);
		float distancesX = area.x / line;
		float distancesY = area.y / row;

		for (int j = 0; j < row; j++)
		{
			for (int k = 0; k < line; k++)
			{
				Vector2 v2 = new Vector2(distancesX * k - 0.5f, distancesY * j - 0.5f);
				PointData data = new PointData();
				data.pos = v2;
				data.status = PointState.wait;
				listPoint.Add(data);
			}
		}
		distancesX *= 3;
		distancesY *= 3;
		for (float x = -0.5f; x <= 0.5; x += distancesX)
		{
			CreateSideCollider(new Vector3(x, -0.5f, 0));
			CreateSideCollider(new Vector3(x, 0.5f, 0));
			CreateSideCollider(new Vector3(-0.5f, x, 0));
			CreateSideCollider(new Vector3(0.5f, x, 0));
		}
	}

	public void CreateSideCollider(Vector3 v3)
	{
		if (goPreCollider == null)
		{
			goPreCollider = Resources.Load("MousePoint3") as GameObject;
		}

		GameObject go = LeanPool.Spawn(goPreCollider, Vector2.zero, Quaternion.Euler(Vector3.zero), target.transform) as GameObject;
		go.transform.localPosition = v3;
		go.transform.localScale = new Vector3(ColliderSize / scale.x, ColliderSize / scale.y, ColliderSize);
		PointData data = new PointData();
		data.pos = new Vector2(v3.x, v3.y);
		data.status = PointState.collid;
		data.col = go;
		listPoint.Add(data);
	}

	public bool Dig()
	{
		if (diggerHead == null)
		{
			return false;
		}
		if (diggerHead.state != DiggerState.Dig)
		{
			return false;
		}

		//创建一条射线一摄像机为原点
		Ray ray = mainCam.ScreenPointToRay(mainCam.WorldToScreenPoint(diggerHead.transform.position));
		RaycastHit hit;
		//射线碰撞到游戏地形时
		if (Physics.Raycast(ray, out hit))
		{
			//从世界坐标转为局部坐标
			Vector2 localCenter = target.transform.InverseTransformPoint(hit.point);
			for (int i = 0; i < listPoint.Count; i++)
			{
				Vector2 centerPos = listPoint[i].pos - localCenter;
				centerPos.x *= scale.x;
				centerPos.y *= scale.y;
				float dis = Vector2.Distance(centerPos, Vector2.zero);
				if (dis < Circle1Range1 && listPoint[i].status != PointState.die)
				{
					listPoint[i].status = PointState.die;
					if (listPoint[i].col != null)
					{
						LeanPool.Despawn(listPoint[i].col);
					}
				}
				else if (dis >= Circle1Range1 && dis < Circle1Range2 && listPoint[i].status == PointState.wait)
				{
					listPoint[i].status = PointState.collid;
					GameObject go = LeanPool.Spawn(goPreCollider, Vector2.zero, Quaternion.Euler(Vector3.zero), target.transform);
					go.transform.localPosition = listPoint[i].pos;
					go.transform.localScale = new Vector3(ColliderSize / scale.x, ColliderSize / scale.y, ColliderSize);
					listPoint[i].col = go;
				}
			}
			bool canCreatePiece = false;
			for (int i = 0; i < spriteErasers.Count; i++)
			{
				if (spriteErasers[i].DoEraser())
				{
					canCreatePiece = true;
				}
			}
			return canCreatePiece;
		}
		return false;
	}
}
