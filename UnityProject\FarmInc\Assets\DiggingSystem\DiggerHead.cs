using UnityEngine;
using System.Collections;
using Lean.Pool;
using DG.Tweening;
using Sirenix.OdinInspector;
public enum DiggerState
{
	None = 0,
	Dig = 1,
	Cleaner,
}
public class DiggerHead : MonoBehaviour
{
	public float moveSpeed = 5f;
	float createPieceInterval = 0.05f;
	private GameObject m_PiecePre;
	public DiggerState state = DiggerState.None;
	public GameObject goDigger;
	public GameObject goCleaner;
	public Rigidbody2D rigidbody2D;
	void Start()
	{
		m_PiecePre = Resources.Load("StonePiece") as GameObject;
		Change2Dig();
	}

	void Update()
	{
		if (Input.GetMouseButton(0))
		{
			// 将鼠标位置转换为世界坐标
			Vector3 worldPos = DigGameMgr.S.mainCam.ScreenToWorldPoint(Input.mousePosition);
			worldPos.z = 0;
			var dir = worldPos - this.transform.position;
			// 更新挖掘头的位置
			transform.position += dir.normalized * Time.deltaTime * moveSpeed;
			if (state == DiggerState.Dig)
			{
				//超过间隔就生成碎片
				createPieceInterval -= Time.deltaTime;
				if (DigGameMgr.S.Dig())
				{
					if (createPieceInterval < 0)
					{
						createPieceInterval = 0.1f;
						CreatePiece();
					}
				}
			}
			else if (state == DiggerState.Cleaner)
			{
				//吸附附近范围内的碎片
				var pieces = GameObject.FindGameObjectsWithTag("DiggingPiece");
				foreach (var item in pieces)
				{
					var dirPiece = item.transform.position - this.transform.position;
					if (dirPiece.magnitude < 1f)
					{
						//吸附过来再回收
						var piece = item.GetComponent<DiggerPiece>();
						piece.Move2Head(this.transform.position);
					}
				}
			}
		}
	}

	public void CreatePiece()
	{
		var go = LeanPool.Spawn(m_PiecePre, transform.position, Quaternion.Euler(Vector3.zero));
		go.transform.localScale = Vector3.one * Random.Range(0.3f, 0.6f);
	}

	[Button("Change2Dig")]
	public void Change2Dig()
	{
		state = DiggerState.Dig;
		goDigger.SetActive(true);
		goCleaner.SetActive(false);
		rigidbody2D.isKinematic = true;
	}
	[Button("Change2Cleaner")]
	public void Change2Cleaner()
	{
		state = DiggerState.Cleaner;
		goDigger.SetActive(false);
		goCleaner.SetActive(true);
		rigidbody2D.isKinematic = false;
	}


}