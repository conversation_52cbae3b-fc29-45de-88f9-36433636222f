using UnityEngine;
using System.Collections;

public class SpriteEraser : MonoBehaviour
{
	public SpriteRenderer targetSprite;
	public float eraserRadius = 0.5f;

	private Texture2D originalTexture;
	private Texture2D editableTexture;
	private Sprite originalSprite;
	private Sprite editableSprite;



	void Start()
	{
		// 保存原始精灵
		originalSprite = targetSprite.sprite;
		originalTexture = originalSprite.texture;

		// 创建可编辑的纹理副本，使用RGBA32格式确保支持SetPixels
		editableTexture = new Texture2D(
			originalTexture.width,
			originalTexture.height,
			TextureFormat.RGBA32,
			false
		);

		// 获取原始纹理的像素数据
		Color[] pixels;
		try
		{
			// 尝试直接获取像素
			pixels = originalTexture.GetPixels();
		}
		catch (UnityException)
		{
			// 如果原始纹理不可读，创建一个默认的白色纹理
			pixels = new Color[originalTexture.width * originalTexture.height];
			for (int i = 0; i < pixels.Length; i++)
			{
				pixels[i] = Color.white;
			}
			Debug.LogWarning("Original texture is not readable. Using default white texture instead.");
		}

		// 设置像素并应用
		editableTexture.SetPixels(pixels);
		editableTexture.Apply();

		// 应用新纹理到精灵
		Rect rect = new Rect(0, 0, editableTexture.width, editableTexture.height);
		Vector2 pivot = originalSprite.pivot / originalSprite.rect.size;
		editableSprite = Sprite.Create(
			editableTexture,
			rect,
			pivot,
			originalSprite.pixelsPerUnit,
			0,
			SpriteMeshType.Tight,
			originalSprite.border
		);

		targetSprite.sprite = editableSprite;
	}

	public bool DoEraser()
	{
		if (DigGameMgr.S.diggerHead.state == DiggerState.Dig)
		{
			// 擦除像素
			// 将鼠标位置转换为世界坐标
			Vector3 worldPos = DigGameMgr.S.mainCam.ScreenToWorldPoint(DigGameMgr.S.mainCam.WorldToScreenPoint(DigGameMgr.S.diggerHead.transform.position));
			worldPos.z = 0;
			//判断坐标在不在sprite范围内
			if (targetSprite.bounds.Contains(worldPos))
			{
				return ErasePixels(worldPos, eraserRadius);
			}
		}
		return false;
	}

	public bool ErasePixels(Vector2 worldPosition, float radius)
	{
		// 将世界坐标转换为精灵局部坐标
		Vector2 localPos = targetSprite.transform.InverseTransformPoint(worldPosition);

		// 获取精灵的边界和尺寸信息
		Bounds bounds = editableSprite.bounds;
		Rect rect = editableSprite.rect;

		// 将局部坐标转换为纹理UV坐标
		// 注意: 精灵的局部坐标原点在中心点，而UV坐标原点在左下角
		Vector2 uv = new Vector2(
			(localPos.x - bounds.min.x) / bounds.size.x,
			(localPos.y - bounds.min.y) / bounds.size.y
		);

		// 确保UV坐标在[0,1]范围内
		uv.x = Mathf.Clamp01(uv.x);
		uv.y = Mathf.Clamp01(uv.y);

		// 计算纹理像素坐标
		int x = Mathf.RoundToInt(uv.x * editableTexture.width);
		int y = Mathf.RoundToInt(uv.y * editableTexture.height);

		// 计算擦除半径（像素单位）
		int radiusPixels = Mathf.RoundToInt(radius * editableTexture.width / bounds.size.x);
		bool canCreatePiece = false;

		// 擦除圆形区域内的像素
		for (int i = -radiusPixels; i <= radiusPixels; i++)
		{
			for (int j = -radiusPixels; j <= radiusPixels; j++)
			{
				if (i * i + j * j <= radiusPixels * radiusPixels)
				{
					int pixelX = x + i;
					int pixelY = y + j;

					if (editableTexture.GetPixel(pixelX, pixelY).a == 0)
					{
						continue;
					}

					if (pixelX >= 0 && pixelX < editableTexture.width &&
						pixelY >= 0 && pixelY < editableTexture.height)
					{
						// 将像素设置为完全透明
						editableTexture.SetPixel(pixelX, pixelY, Color.clear);
						canCreatePiece = true;
					}
				}
			}
		}

		// 应用纹理更改
		editableTexture.Apply();
		return canCreatePiece;
	}

	// 重置纹理到原始状态
	public void ResetTexture()
	{
		// 获取原始纹理的像素数据
		Color[] pixels;
		try
		{
			pixels = originalTexture.GetPixels();
		}
		catch (UnityException)
		{
			// 如果原始纹理不可读，创建一个默认的白色纹理
			pixels = new Color[originalTexture.width * originalTexture.height];
			for (int i = 0; i < pixels.Length; i++)
			{
				pixels[i] = Color.white;
			}
		}

		editableTexture.SetPixels(pixels);
		editableTexture.Apply();
	}

	// // 调试方法：在场景中显示点击位置
	// void OnDrawGizmos()
	// {
	// 	if (Application.isPlaying && Input.GetMouseButton(0))
	// 	{
	// 		Vector3 worldPos = Camera.main.ScreenToWorldPoint(Input.mousePosition);
	// 		worldPos.z = 0;

	// 		Gizmos.color = Color.red;
	// 		Gizmos.DrawWireSphere(worldPos, eraserRadius);

	// 		// 转换为精灵局部坐标
	// 		Vector2 localPos = targetSprite.transform.InverseTransformPoint(worldPos);

	// 		// 转换为UV坐标
	// 		Bounds bounds = editableSprite.bounds;
	// 		Vector2 uv = new Vector2(
	// 			(localPos.x - bounds.min.x) / bounds.size.x,
	// 			(localPos.y - bounds.min.y) / bounds.size.y
	// 		);

	// 		// 转换为像素坐标
	// 		int x = Mathf.RoundToInt(uv.x * editableTexture.width);
	// 		int y = Mathf.RoundToInt(uv.y * editableTexture.height);

	// 		//			Debug.Log($"World: {worldPos}, Local: {localPos}, UV: {uv}, Pixel: ({x}, {y})");
	// 	}
	// }
}